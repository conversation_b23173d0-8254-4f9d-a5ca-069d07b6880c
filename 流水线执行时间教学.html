<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流水线执行时间 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 2.5em;
            margin-bottom: 60px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            animation: fadeInDown 1s ease-out;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: slideInUp 0.8s ease-out;
        }

        .section h2 {
            color: #4a5568;
            font-size: 1.8em;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .problem-box {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            color: #2d3748;
            font-size: 1.1em;
            line-height: 1.8;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .pipeline-demo {
            background: #f7fafc;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .stage {
            display: inline-block;
            width: 120px;
            height: 80px;
            margin: 10px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .fetch { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
        .decode { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
        .execute { background: linear-gradient(135deg, #45b7d1, #96c93d); }

        .stage:hover {
            transform: scale(1.1);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .timeline {
            margin: 30px 0;
            position: relative;
        }

        .time-unit {
            display: inline-block;
            width: 60px;
            height: 40px;
            border: 2px solid #e2e8f0;
            margin: 2px;
            text-align: center;
            line-height: 36px;
            font-size: 12px;
            background: white;
            transition: all 0.3s ease;
        }

        .time-unit.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: scale(1.1);
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .formula-box {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            text-align: center;
            font-size: 1.3em;
            font-weight: bold;
            color: #2d3748;
        }

        .answer-reveal {
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            text-align: center;
            font-size: 1.2em;
            color: #2d3748;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .answer-reveal.show {
            opacity: 1;
            transform: translateY(0);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .instruction-flow {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .instruction {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            min-width: 200px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .instruction:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 10px;
        }

        .understanding-box {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
        }

        .understanding-box h3 {
            color: #2d3748;
            text-align: center;
            margin-bottom: 20px;
        }

        .analogy-demo {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .step-analysis {
            margin: 30px 0;
        }

        .step-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateX(-20px);
            transition: all 0.5s ease;
        }

        .step-card.show {
            opacity: 1;
            transform: translateX(0);
        }

        .step-card h4 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .visual-explanation {
            margin: 15px 0;
        }

        .instruction-timeline {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .time-block {
            padding: 15px 20px;
            margin: 5px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            text-align: center;
            min-width: 100px;
        }

        .fetch-block { background: #ff6b6b; }
        .decode-block { background: #4ecdc4; }
        .execute-block { background: #45b7d1; }

        .pipeline-comparison {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .comparison-item {
            background: #f7fafc;
            border-radius: 10px;
            padding: 20px;
            margin: 10px;
            flex: 1;
            min-width: 300px;
        }

        .comparison-item h5 {
            text-align: center;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .wrong-timeline, .correct-timeline {
            margin: 15px 0;
        }

        .instruction-block {
            background: #fed7d7;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            text-align: center;
            border: 2px solid #fc8181;
        }

        .overlap-demo {
            margin: 15px 0;
        }

        .inst-row {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }

        .inst-row span {
            width: 80px;
            font-weight: bold;
            color: #4a5568;
        }

        .stage-box {
            width: 60px;
            height: 30px;
            margin: 2px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: white;
            font-weight: bold;
        }

        .stage-box.fetch { background: #ff6b6b; }
        .stage-box.decode { background: #4ecdc4; }
        .stage-box.execute { background: #45b7d1; }
        .stage-box.empty { background: transparent; border: 1px dashed #cbd5e0; }

        .counting-demo {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .count-item {
            text-align: center;
            margin: 10px;
        }

        .count-box {
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 10px;
            font-weight: bold;
            color: white;
        }

        .count-box.first { background: #667eea; }
        .count-box.remaining { background: #48bb78; }

        .formula-breakdown {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .formula-part {
            text-align: center;
            margin: 10px;
        }

        .formula-component {
            background: #667eea;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .formula-component.first-part { background: #ff6b6b; }
        .formula-component.remaining-part { background: #48bb78; }

        .formula-meaning {
            font-size: 0.9em;
            color: #4a5568;
            font-style: italic;
        }

        .formula-plus {
            font-size: 2em;
            font-weight: bold;
            color: #4a5568;
            margin: 0 20px;
        }

        .formula-essence {
            text-align: center;
            font-size: 1.2em;
            color: #2d3748;
            background: #e6fffa;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🚀 流水线执行时间计算 - 互动学习</h1>
        
        <div class="section">
            <h2>📚 题目展示</h2>
            <div class="problem-box">
                <p><strong>题目：</strong>执行指令时，将每一节指令都分解为取指、分析和执行三步，已知取指时间t取指=5△t，分析时间t分析=2△t，执行时间t执行=3△t。如果按照重叠的流水线方式执行指令，从头到尾执行完500条指令需（ ）△t。</p>
                <br>
                <p><strong>选项：</strong></p>
                <p>A. 2500　　B. 2505　　C. 2510　　D. 2515</p>
                <p><strong>正确答案：B (2505)</strong></p>
            </div>
        </div>

        <div class="section">
            <h2>🔍 基础概念理解</h2>
            <div class="instruction-flow">
                <div class="instruction">
                    <div class="stage fetch">取指</div>
                    <p><strong>取指阶段</strong><br>时间：5△t<br>从内存中获取指令</p>
                </div>
                <div class="instruction">
                    <div class="stage decode">分析</div>
                    <p><strong>分析阶段</strong><br>时间：2△t<br>解码指令内容</p>
                </div>
                <div class="instruction">
                    <div class="stage execute">执行</div>
                    <p><strong>执行阶段</strong><br>时间：3△t<br>执行具体操作</p>
                </div>
            </div>
            <p style="text-align: center; margin-top: 20px; font-size: 1.1em; color: #4a5568;">
                💡 <strong>关键理解：</strong>流水线周期 = 最长阶段时间 = max(5, 2, 3) = 5△t
            </p>
        </div>

        <div class="section">
            <h2>⚡ 流水线动画演示</h2>
            <div class="pipeline-demo">
                <canvas id="pipelineCanvas" width="1000" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="startAnimation()">🎬 开始演示</button>
                <button class="btn" onclick="resetAnimation()">🔄 重置</button>
                <button class="btn" onclick="showFormula()">📐 显示公式</button>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section">
            <h2>🤔 公式理解 - 不要背，要懂！</h2>

            <div class="understanding-box">
                <h3>🎯 核心思想：流水线就像工厂流水线</h3>
                <div class="analogy-demo">
                    <canvas id="factoryCanvas" width="1000" height="300"></canvas>
                    <div class="controls">
                        <button class="btn" onclick="startFactoryDemo()">🏭 工厂流水线演示</button>
                        <button class="btn" onclick="startCPUDemo()">💻 CPU流水线演示</button>
                        <button class="btn" onclick="showStepByStep()">📝 逐步分析</button>
                    </div>
                </div>
            </div>

            <div class="step-analysis" id="stepAnalysis" style="display: none;">
                <h3>🔍 逐步理解公式</h3>

                <div class="step-card" id="step1">
                    <h4>第1步：理解"第一条指令完成时间"</h4>
                    <div class="visual-explanation">
                        <div class="instruction-timeline">
                            <div class="time-block fetch-block">取指 5△t</div>
                            <div class="time-block decode-block">分析 2△t</div>
                            <div class="time-block execute-block">执行 3△t</div>
                        </div>
                        <p><strong>💡 关键理解：</strong>第一条指令必须完整走完所有阶段才算完成</p>
                        <p>所以第一条指令需要：5 + 2 + 3 = 10△t</p>
                    </div>
                </div>

                <div class="step-card" id="step2">
                    <h4>第2步：理解"流水线周期"</h4>
                    <div class="visual-explanation">
                        <p><strong>🤔 思考：</strong>为什么是5△t而不是10△t？</p>
                        <div class="pipeline-comparison">
                            <div class="comparison-item">
                                <h5>❌ 错误理解：每条指令都要10△t</h5>
                                <div class="wrong-timeline">
                                    <div class="instruction-block">指令1: 10△t</div>
                                    <div class="instruction-block">指令2: 10△t</div>
                                    <div class="instruction-block">指令3: 10△t</div>
                                </div>
                                <p>总时间：500 × 10 = 5000△t</p>
                            </div>
                            <div class="comparison-item">
                                <h5>✅ 正确理解：重叠执行</h5>
                                <div class="correct-timeline">
                                    <div class="overlap-demo">
                                        <div class="inst-row">
                                            <span>指令1:</span>
                                            <div class="stage-box fetch">取指</div>
                                            <div class="stage-box decode">分析</div>
                                            <div class="stage-box execute">执行</div>
                                        </div>
                                        <div class="inst-row">
                                            <span>指令2:</span>
                                            <div class="stage-box empty"></div>
                                            <div class="stage-box fetch">取指</div>
                                            <div class="stage-box decode">分析</div>
                                            <div class="stage-box execute">执行</div>
                                        </div>
                                    </div>
                                </div>
                                <p><strong>💡 关键：</strong>每5△t就能完成一条新指令！</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="step-card" id="step3">
                    <h4>第3步：理解"(指令数-1)"</h4>
                    <div class="visual-explanation">
                        <p><strong>🤔 为什么要减1？</strong></p>
                        <div class="counting-demo">
                            <div class="count-item">
                                <div class="count-box first">第1条指令</div>
                                <p>需要完整的10△t</p>
                            </div>
                            <div class="count-item">
                                <div class="count-box remaining">剩余499条指令</div>
                                <p>每条只需要5△t（因为重叠）</p>
                            </div>
                        </div>
                        <p><strong>💡 所以：</strong>总时间 = 10 + 499 × 5 = 2505△t</p>
                    </div>
                </div>
            </div>

            <div class="formula-box" id="formulaBox" style="display: none;">
                <h3>📐 公式本质理解</h3>
                <div class="formula-breakdown">
                    <div class="formula-part">
                        <div class="formula-component first-part">第一条指令完成时间</div>
                        <div class="formula-meaning">= 启动时间（必须等第一条完全完成）</div>
                    </div>
                    <div class="formula-plus">+</div>
                    <div class="formula-part">
                        <div class="formula-component remaining-part">(指令数-1) × 流水线周期</div>
                        <div class="formula-meaning">= 剩余指令的重叠执行时间</div>
                    </div>
                </div>
                <p class="formula-essence"><strong>🎯 公式本质：</strong>启动时间 + 重叠执行时间</p>
            </div>

            <div class="answer-reveal" id="answerReveal">
                <h3>🎉 现在你理解了吗？</h3>
                <p>流水线就像接力赛：</p>
                <ul>
                    <li>🏃‍♂️ 第一个人要跑完全程</li>
                    <li>🏃‍♀️ 后面的人可以在前面人还没跑完时就开始</li>
                    <li>⏱️ 每隔一个"周期"就有一个人到达终点</li>
                </ul>
                <p><strong>答案：B. 2505△t</strong></p>
            </div>
        </div>
    </div>

    <script>
        let canvas, ctx;
        let animationId;
        let currentStep = 0;
        let isAnimating = false;

        window.onload = function() {
            canvas = document.getElementById('pipelineCanvas');
            ctx = canvas.getContext('2d');
            drawInitialState();
        };

        function drawInitialState() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制标题
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('流水线执行示意图', canvas.width/2, 30);
            
            // 绘制时间轴
            ctx.fillStyle = '#718096';
            ctx.font = '14px Microsoft YaHei';
            for(let i = 0; i < 15; i++) {
                ctx.fillText(`${i}△t`, 50 + i * 60, 70);
            }
            
            // 绘制指令标签
            const instructions = ['指令1', '指令2', '指令3', '指令4', '指令5'];
            for(let i = 0; i < 5; i++) {
                ctx.fillStyle = '#2d3748';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'right';
                ctx.fillText(instructions[i], 40, 120 + i * 60);
            }
        }

        function startAnimation() {
            if(isAnimating) return;
            isAnimating = true;
            currentStep = 0;
            animateStep();
        }

        function animateStep() {
            drawInitialState();
            
            // 绘制流水线执行过程
            const colors = {
                fetch: '#ff6b6b',
                decode: '#4ecdc4', 
                execute: '#45b7d1'
            };
            
            for(let inst = 0; inst < Math.min(5, Math.floor(currentStep/5) + 1); inst++) {
                let startTime = inst * 5;
                
                // 取指阶段
                if(currentStep >= startTime) {
                    drawStage(startTime, inst, 5, colors.fetch, '取指');
                }
                
                // 分析阶段  
                if(currentStep >= startTime + 5) {
                    drawStage(startTime + 5, inst, 2, colors.decode, '分析');
                }
                
                // 执行阶段
                if(currentStep >= startTime + 7) {
                    drawStage(startTime + 7, inst, 3, colors.execute, '执行');
                }
            }
            
            // 更新进度条
            const progress = Math.min(100, (currentStep / 50) * 100);
            document.getElementById('progressFill').style.width = progress + '%';
            
            currentStep++;
            
            if(currentStep < 50 && isAnimating) {
                setTimeout(() => animateStep(), 200);
            } else {
                isAnimating = false;
            }
        }

        function drawStage(startTime, instIndex, duration, color, label) {
            const x = 50 + startTime * 6;
            const y = 100 + instIndex * 60;
            const width = duration * 6;
            const height = 40;
            
            // 绘制阶段矩形
            ctx.fillStyle = color;
            ctx.fillRect(x, y, width, height);
            
            // 绘制边框
            ctx.strokeStyle = '#2d3748';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, width, height);
            
            // 绘制标签
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, x + width/2, y + height/2 + 4);
        }

        function resetAnimation() {
            isAnimating = false;
            currentStep = 0;
            drawInitialState();
            document.getElementById('progressFill').style.width = '0%';
        }

        function showFormula() {
            const formulaBox = document.getElementById('formulaBox');
            const answerReveal = document.getElementById('answerReveal');

            formulaBox.style.display = 'block';
            formulaBox.style.animation = 'pulse 0.5s ease-in-out';

            setTimeout(() => {
                answerReveal.classList.add('show');
            }, 1000);
        }

        function startFactoryDemo() {
            const canvas = document.getElementById('factoryCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制工厂流水线比喻
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('🏭 工厂流水线：制造汽车', canvas.width/2, 30);

            // 绘制工位
            const stations = ['🔧 组装', '🎨 喷漆', '✅ 检验'];
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1'];

            for(let i = 0; i < 3; i++) {
                ctx.fillStyle = colors[i];
                ctx.fillRect(100 + i * 200, 80, 150, 60);

                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Microsoft YaHei';
                ctx.fillText(stations[i], 175 + i * 200, 115);

                ctx.fillStyle = '#2d3748';
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText(`${[5,2,3][i]}分钟`, 175 + i * 200, 135);
            }

            // 绘制汽车流动
            animateFactory(ctx, 0);
        }

        function animateFactory(ctx, step) {
            if(step > 20) return;

            // 清除汽车区域
            ctx.clearRect(0, 160, canvas.width, 100);

            // 绘制汽车
            const carPositions = [
                {x: 175, y: 180, stage: '组装中', color: '#ff6b6b'},
                {x: 375, y: 180, stage: '喷漆中', color: '#4ecdc4'},
                {x: 575, y: 180, stage: '检验中', color: '#45b7d1'}
            ];

            for(let i = 0; i < Math.min(3, Math.floor(step/3) + 1); i++) {
                const car = carPositions[i];
                ctx.fillStyle = car.color;
                ctx.fillRect(car.x - 20, car.y, 40, 20);

                ctx.fillStyle = '#2d3748';
                ctx.font = '10px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(`🚗${i+1}`, car.x, car.y + 35);
                ctx.fillText(car.stage, car.x, car.y + 50);
            }

            setTimeout(() => animateFactory(ctx, step + 1), 500);
        }

        function startCPUDemo() {
            const canvas = document.getElementById('factoryCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制CPU流水线
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('💻 CPU流水线：执行指令', canvas.width/2, 30);

            // 绘制阶段
            const stages = ['取指', '分析', '执行'];
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1'];
            const times = ['5△t', '2△t', '3△t'];

            for(let i = 0; i < 3; i++) {
                ctx.fillStyle = colors[i];
                ctx.fillRect(100 + i * 200, 80, 150, 60);

                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Microsoft YaHei';
                ctx.fillText(stages[i], 175 + i * 200, 110);
                ctx.fillText(times[i], 175 + i * 200, 130);
            }

            // 绘制关键理解
            ctx.fillStyle = '#e53e3e';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.fillText('💡 关键：每5△t完成一条指令（最长阶段决定周期）', canvas.width/2, 200);

            ctx.fillStyle = '#38a169';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('第1条指令：5+2+3=10△t', canvas.width/2, 230);
            ctx.fillText('第2条指令：只需再等5△t（重叠执行）', canvas.width/2, 250);
            ctx.fillText('第3条指令：再等5△t...', canvas.width/2, 270);
        }

        function showStepByStep() {
            const stepAnalysis = document.getElementById('stepAnalysis');
            stepAnalysis.style.display = 'block';

            // 逐步显示每个步骤
            const steps = ['step1', 'step2', 'step3'];
            steps.forEach((stepId, index) => {
                setTimeout(() => {
                    document.getElementById(stepId).classList.add('show');
                }, index * 1000);
            });

            // 最后显示公式
            setTimeout(() => {
                showFormula();
            }, 3500);
        }

        // 添加交互效果
        document.querySelectorAll('.stage').forEach(stage => {
            stage.addEventListener('click', function() {
                this.style.animation = 'pulse 0.5s ease-in-out';
                setTimeout(() => {
                    this.style.animation = '';
                }, 500);
            });
        });
    </script>
</body>
</html>
