<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二进制除法教学</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .division-demo {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #4CAF50;
        }
        
        .binary-number {
            font-family: 'Courier New', monospace;
            font-size: 18px;
            font-weight: bold;
            color: #2196F3;
            letter-spacing: 2px;
        }
        
        .step {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #ddd;
            transition: all 0.3s ease;
        }
        
        .step:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .step-number {
            background: #4CAF50;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        
        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { background-color: #ffeb3b; }
            50% { background-color: #ffc107; }
            100% { background-color: #ffeb3b; }
        }
        
        .interactive-section {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #45a049;
            transform: scale(1.05);
        }
        
        .explanation {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }
        
        canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔢 二进制除法教学</h1>
            <p>理解您图片中的二进制长除法过程</p>
        </div>
        
        <div class="content">
            <div class="division-demo">
                <h2>📊 您的图片分析</h2>
                <p>您的图片展示了一个二进制除法的长除法过程，类似于十进制的长除法，但使用的是二进制数字（0和1）。</p>
                
                <div class="explanation">
                    <strong>图片中的关键元素：</strong>
                    <ul>
                        <li><span class="binary-number">被除数</span>：位于除法符号内部的长二进制数</li>
                        <li><span class="binary-number">除数</span>：位于除法符号左侧的较短二进制数</li>
                        <li><span class="binary-number">商</span>：位于除法符号上方的结果</li>
                        <li><span class="binary-number">余数处理</span>：每一步的减法运算</li>
                    </ul>
                </div>
            </div>
            
            <div class="interactive-section">
                <h3>🎯 交互式二进制除法演示</h3>
                <canvas id="divisionCanvas" width="800" height="400"></canvas>
                <br>
                <button class="btn" onclick="startDemo()">开始演示</button>
                <button class="btn" onclick="nextStep()">下一步</button>
                <button class="btn" onclick="resetDemo()">重置</button>
            </div>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>二进制除法基本规则：</strong>
                <div class="explanation">
                    <p>二进制除法遵循与十进制相同的长除法原理：</p>
                    <ul>
                        <li>0 ÷ 1 = 0</li>
                        <li>1 ÷ 1 = 1</li>
                        <li>如果被除数小于除数，商为0</li>
                        <li>如果被除数大于等于除数，商为1，然后减去除数</li>
                    </ul>
                </div>
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>长除法步骤：</strong>
                <div class="explanation">
                    <p>每一步都包含：</p>
                    <ol>
                        <li>比较当前位数与除数</li>
                        <li>确定商的当前位（0或1）</li>
                        <li>如果商为1，执行减法</li>
                        <li>下移下一位，重复过程</li>
                    </ol>
                </div>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>实际应用：</strong>
                <div class="explanation">
                    <p>二进制除法在计算机科学中的应用：</p>
                    <ul>
                        <li>CPU算术逻辑单元(ALU)运算</li>
                        <li>数字信号处理</li>
                        <li>加密算法</li>
                        <li>数据压缩算法</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('divisionCanvas');
        const ctx = canvas.getContext('2d');
        let currentStep = 0;
        let animationId;
        
        // 示例：1010111 ÷ 101 = 1001 余 10
        const dividend = '1010111';
        const divisor = '101';
        const quotient = '1001';
        const steps = [
            { desc: '开始：被除数 1010111，除数 101', highlight: [0, 2] },
            { desc: '101 > 101，商的第一位是1', highlight: [0, 2], subtract: true },
            { desc: '余数00，下移下一位得001', highlight: [3, 3] },
            { desc: '001 < 101，商的下一位是0', highlight: [3, 3] },
            { desc: '下移得0011', highlight: [3, 4] },
            { desc: '0011 < 101，商的下一位是0', highlight: [3, 4] },
            { desc: '下移得00111', highlight: [3, 5] },
            { desc: '111 > 101，商的最后一位是1', highlight: [5, 6], subtract: true }
        ];
        
        function drawDivision() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.font = '20px Courier New';
            ctx.fillStyle = '#333';
            
            // 绘制除法框架
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            
            // 除法符号
            ctx.beginPath();
            ctx.moveTo(100, 100);
            ctx.lineTo(300, 100);
            ctx.moveTo(100, 100);
            ctx.lineTo(100, 200);
            ctx.stroke();
            
            // 除数
            ctx.fillText(divisor, 50, 150);
            
            // 被除数
            ctx.fillText(dividend, 120, 150);
            
            // 商
            if (currentStep > 0) {
                ctx.fillStyle = '#4CAF50';
                ctx.fillText(quotient.substring(0, Math.min(currentStep, quotient.length)), 120, 80);
            }
            
            // 当前步骤说明
            if (currentStep < steps.length) {
                ctx.fillStyle = '#666';
                ctx.font = '16px Arial';
                ctx.fillText(steps[currentStep].desc, 50, 250);
                
                // 高亮显示
                if (steps[currentStep].highlight) {
                    ctx.fillStyle = 'rgba(255, 235, 59, 0.5)';
                    const [start, end] = steps[currentStep].highlight;
                    const x = 120 + start * 12;
                    const width = (end - start + 1) * 12;
                    ctx.fillRect(x, 135, width, 25);
                }
            }
        }
        
        function startDemo() {
            currentStep = 0;
            drawDivision();
        }
        
        function nextStep() {
            if (currentStep < steps.length - 1) {
                currentStep++;
                drawDivision();
            }
        }
        
        function resetDemo() {
            currentStep = 0;
            drawDivision();
        }
        
        // 初始化
        drawDivision();
        
        // 添加动画效果
        function animate() {
            // 简单的闪烁效果
            if (currentStep < steps.length && steps[currentStep].highlight) {
                drawDivision();
            }
            animationId = requestAnimationFrame(animate);
        }
        
        animate();
    </script>
</body>
</html>
